import configparser
import json
import multiprocessing
import os
import time
from datetime import datetime

import redis
import weasyprint
import yagmail
from PyPDF2 import PdfWriter, Pdf<PERSON>eader

def create_pdf(html_file):
    # 调用weasyprint脚本生成pdf
    pdf_file = html_file.replace('.html', '.pdf')
    weasyprint.HTML(filename=html_file).write_pdf(pdf_file)
    print('生成pdf成功')

def add_password_to_pdf(input_pdf, output_pdf, password):
    # 创建一个PdfWriter对象
    pdf_writer = PdfWriter()

    # 打开输入的PDF文件
    with open(input_pdf, 'rb') as file:
        pdf_reader = PdfReader(file)

        # 遍历PDF的所有页面并添加到PdfWriter对象中
        for page_num in range(len(pdf_reader.pages)):
            page = pdf_reader.pages[page_num]
            pdf_writer.add_page(page)

        # 设置密码
        pdf_writer.encrypt(password)

        # 将加密后的PDF写入输出文件
        with open(output_pdf, 'wb') as output_file:
            pdf_writer.write(output_file)

def main():


    config = configparser.ConfigParser()
    config.read('.env')

    # 连接redis
    host = config['redis']['host']
    # print(host)
    port = config['redis']['port']
    db = config['redis']['db']
    password = config['redis']['password']
    redis_client = redis.Redis(host=host, port=port, db=db, password=password)

    while True:
        keys = redis_client.keys('js-pdf-*')

        for key in keys:
            data = redis_client.get(key)
            if not data:
                continue

            begin_time = time.time()

            data = json.loads(data)
            flow = data['flow']
            pdf_path = data['pdf_path']
            html_files = data['html_files']
            user = data['user']
            card = data['card']
            local_time = datetime.fromtimestamp(flow['createtime'])

            num_processes = int(os.cpu_count() / 2)
            print('开始生成pdf，进程数：', num_processes)
            with multiprocessing.Pool(processes=num_processes) as pool:
                # 调用weasyprint脚本生成pdf
                for html_file in html_files:
                    pool.apply_async(create_pdf, (html_file,))

                # 等待所有线程结束
                pool.close()
                pool.join()

            # 合并pdf
            # 调用系统命令行合并pdf

            random_code = str(int(time.time()))[-9:]

            pdf_name = "hqmx_" + local_time.strftime("%Y%m%d%H%M%S")
            pdf_file = os.path.join(pdf_path, pdf_name)
            pdf_files = []
            print(user['id'])
            # 如果用id等于1449,则分页合并，每30个合并成一个
            if card['multi_page'] == 1:
                # 分页合并
                # 每30个合并成一个
                for i in range(0, len(html_files), 30):
                    cmd = 'pdfunite '
                    for html_file in html_files[i:i+30]:
                        pdf_file = html_file.replace('.html', '.pdf')
                        cmd += pdf_file + ' '
                    cmd += pdf_path + '/' + pdf_name+'_0000'+str(len(pdf_files)+1)+'.pdf'
                    os.system(cmd)
                    print('合并pdf成功',cmd)
                    pdf_files.append(pdf_path + '/' + pdf_name+'_0000'+str(len(pdf_files) + 1)+'.pdf')
            else:
                cmd = 'pdfunite '
                for html_file in html_files:
                    pdf_file = html_file.replace('.html', '.pdf')
                    cmd += pdf_file + ' '
                cmd += pdf_path + '/' + pdf_name+'.pdf'
                os.system(cmd)
                print('合并pdf成功',cmd)
                pdf_files.append(pdf_path + '/' + pdf_name+'.pdf')

            
            # pdf加密码
            # add_password_to_pdf(pdf_path + '/' + pdf_name+'.pdf', pdf_path + '/' + pdf_name+'.pdf', flow['extraction_code'])

            for pdf_file in pdf_files:
                # 压缩成zip
                # 调用系统命令行压缩zip
                cmd = 'zip -r -j -P  ' + flow['extraction_code'] + ' '
                cmd += pdf_file.replace('.pdf', '.zip') + ' '
                cmd += pdf_file + ' '
                os.system(cmd)
            print('压缩zip成功')
            print(cmd)



            # 发送邮箱
            sender = '<EMAIL>'
            password = 'aabb123456@'
            nickname = 'service'
            # 收件人
            receiver = flow['email']
            subject = '中国建设银行个人活期账户交易明细'
            for i in range(len(pdf_files)):
                body = """<div>尊敬的{0}：</div> <div style="margin-left: 10px;">  您好！附件是您于{1}通过我行申请的电子版交易明细，请查收。（共{2}封邮件，当前第{3}封）</div><div style="margin-left: 10px;">  附件已加密，请使用您申请时获取的解压密码打开附件。</div><div style="margin-left: 10px;">  请勿随意转发并妥善保管解压密码及邮件，由于保管不善导致的信息泄漏风险由您自行承担，感谢您对建设银行的支持。</div><div style="margin-left: 10px;">  （交易明细附件）</div><p align="right">中国建设银行股份有限公司</p>"""
                body = body.format(user['name'], local_time.strftime("%Y年%m月%d日 %H:%M:%S"), len(pdf_files), i+1)
                attachment_path = pdf_files[i].replace('.pdf', '.zip')

                yag = yagmail.SMTP(user={sender: nickname}, password=password, host="smtp.qiye.aliyun.com" , port=465)

                contents = body  # 邮件正文

                print(attachment_path)
                yag.send(to=receiver, subject=subject, contents=contents, attachments=attachment_path)

            end_time = time.time()
            print('总共耗时：', end_time - begin_time, 's')

            # 删除key
            redis_client.delete(key)
        pass
        time.sleep(1)
    pass

if __name__ == '__main__':
    main()
    pass