import configparser
import json
import time
from datetime import datetime

from DrawImage import DrawImage
import os,sys
import redis

php_path = ''
output_path = './js/output'
template_path = './js/template/template.png'
zhang_path = './js/template/zhang.png'


def getRandChar():
    # 随机生成6位数字 + 6位随机大写字母

    import random
    import string

    rand_number = ''.join(random.sample(string.digits, 6))
    rand_upper = ''.join(random.sample(string.ascii_uppercase, 6))

    return rand_number + rand_upper
    pass


while True:
    config = configparser.ConfigParser()
    config.read('.env')
    send_email_url = config['app']['url']
    php_path = config['app']['php_path']
    # 连接redis
    host = config['redis']['host']
    port = config['redis']['port']
    db = config['redis']['db']
    password = config['redis']['password']
    redis_client = redis.Redis(host=host, port=port, db=db , password=password)

    # 获取所有js-*的key
    keys = redis_client.keys('js-*')

    for key in keys:
        # 获取流水号
        flow_info = redis_client.get(key)
        
        if not flow_info:
            print("未找到流水号")
            exit()

        flow_info = json.loads(flow_info)

        flow = flow_info['flow']
        
        flow_id = flow['id']
        
        records = flow_info['records']

        begin_time = flow['begin_time']
        end_time = flow['end_time']
        card_number = flow['card_number']
        status = flow['status']
        name = flow['name']
        pdf_path = flow['pdf_path']


        if not records:
            print("未找到交易记录")
            exit()

        # 每19条记录一页
        page_size = 19

        # 计算总页数
        total_page = len(records) // page_size + (1 if len(records) % page_size else 0)

        createtime = time.strftime('%Y-%m-%d')+ ' ' + time.strftime('%H:%M:%S')

        # 清空文件夹
        if os.path.exists(output_path):
            for file in os.listdir(output_path):
                os.remove(os.path.join(output_path, file))
                pass
            pass

        rand_numeber = getRandChar()

        # 打开模板图片
        for page in range(1, total_page + 1):
            image = DrawImage(image_path=template_path)

            # 卡号
            image.drawText(text_color=(0, 0, 0),font_path='font/dazibaoti.ttf',text=card_number,text_position=(200,203),font_size=24)

            # 姓名
            image.drawText(text_color=(0, 0, 0), font_path='font/shanghai.ttf', text=name, text_position=(710, 208), font_size=24)

            # 起始日期
            image.drawText(text_color=(0, 0, 0), font_path='font/dazibaoti.ttf', text=str(begin_time), text_position=(1295, 203), font_size=24)

            # 结束日期
            image.drawText(text_color=(0, 0, 0), font_path='font/dazibaoti.ttf', text=str(end_time), text_position=(1725, 203), font_size=24)

            # 生成日期
            image.drawText(text_color=(0, 0, 0), font_path='font/dazibaoti.ttf', text=createtime, text_position=(185, 1260), font_size=24)

            # 当前页
            image.drawText(text_color=(0, 0, 0), font_path='font/dazibaoti.ttf', text=str(page),  text_position=(1281, 1260), font_size=24)

            # 总页数
            image.drawText(text_color=(0, 0, 0), font_path='font/dazibaoti.ttf', text=str(total_page), text_position=(1526, 1260), font_size=24)

            # 交易记录
            for i in range(1, page_size + 1):
                index = i + (page - 1) * page_size - 1
                if index >= len(records):
                    break

                record = records[index]
                # 序号
                image.drawText(text_color=(0, 0, 0), font_path='font/dazibaoti.ttf', text=str(index+1) , text_position=(84, 336 + (i-1) * 49), font_size=24)
                # 摘要
                summary = record['summary']
                image.drawText(text_color=(0, 0, 0), font_path='font/shanghai.ttf', text=summary, text_position=(145, 336 + (i-1) * 49), font_size=24)
                # 币别
                image.drawText(text_color=(0, 0, 0), font_path='font/dazibaoti.ttf', text="人民币元", text_position=(411, 336 + (i-1) * 49), font_size=24)
                # 钞汇
                image.drawText(text_color=(0, 0, 0), font_path='font/dazibaoti.ttf', text="钞", text_position=(562, 336 + (i-1) * 49), font_size=24)
                # 交易日期
                transaction_date = record['transaction_date']
                image.drawText(text_color=(0, 0, 0), font_path='font/dazibaoti.ttf', text=transaction_date, text_position=(648, 336 + (i-1) * 49), font_size=24)
                # 交易金额
                transaction_amount = float(record['transaction_amount'])
                # 千分号分隔
                transaction_amount = '{:,.2f}'.format(transaction_amount)

                image.drawText(text_color=(0, 0, 0), font_path='font/dazibaoti.ttf', text=str(transaction_amount),  text_position=(932, 336 + (i-1) * 49), font_size=24 , align='right')
                # 交易余额
                balance = float(record['balance'])
                # 千分号分隔
                balance = '{:,.2f}'.format(balance)
                image.drawText(text_color=(0, 0, 0), font_path='font/dazibaoti.ttf', text=str(balance), text_position=(1113, 336 + (i-1) * 49), font_size=24 , align='right')

                # 交易地点/附言
                merchant_code = record['merchant_code']
                if len(merchant_code) > 14:
                    merchant_code = merchant_code[:14] + '\n' + merchant_code[14:]
                    pass

                if '\n' in merchant_code:
                    image.drawText(text_color=(0, 0, 0), font_path='font/shanghai.ttf', text=merchant_code, text_position=(1122, 336 + (i-1) * 49), font_size=24)
                else:
                    image.drawText(text_color=(0, 0, 0), font_path='font/shanghai.ttf', text=merchant_code, text_position=(1122, 340 + (i-1) * 49), font_size=24)
                    pass

                pass

                # 对方账号与户名

                counterparty_account = record['counterparty_account']
                if flow['show_account'] == 0:
                    # 脱敏
                    if len(counterparty_account) < 10:
                        counterparty_account = counterparty_account[:3] + '****' + counterparty_account[-4:]
                    else:
                        counterparty_account = counterparty_account[:(len(counterparty_account) - 9)] + '*****' + counterparty_account[-4:]
                pass

                counterparty_name = record['counterparty_name']
                if flow['show_name'] == 0:
                    # 脱敏
                    counterparty_name = counterparty_name[:1] + '***' + counterparty_name[-(len(counterparty_name)-4):]
                    pass

                counterparty = counterparty_account + '/' + counterparty_name

                star_num = 0
                for j in counterparty:
                    if j == '*':
                        star_num += 1
                        pass
                    pass

                # 换行字数
                line_num = 26 + int(star_num/3)
                # print(counterparty ,len(counterparty) , line_num)

                # 交易对方
                if len(counterparty) > line_num:
                    counterparty = counterparty[:line_num] + '\n' + counterparty[line_num:]
                    pass

                # 交易对方
                if '\n' in counterparty:
                    image.drawText(text_color=(0, 0, 0), font_path='font/shanghai.ttf', text=counterparty, text_position=(1461, 330 + (i-1) * 49), font_size=24 , max_width=10)
                else:
                    image.drawText(text_color=(0, 0, 0), font_path='font/shanghai.ttf', text=counterparty, text_position=(1461, 340 + (i-1) * 49), font_size=24, max_width=10)
                    pass

            image.drawText(text_color=(255, 0, 0), font_path='font/songti.ttf', text=rand_numeber, text_position=(238, 1113), font_size=24)

            # 添加图片
            image.drawImage(image_path=zhang_path, position=(170, 918), width=253, height=251)
            # 添加红字
            # image.drawText(text_color=(255, 0, 0), font_path='font/songti.ttf', text='754202YKJPGV', text_position=(238, 1113), font_size=20)

            # 保存图片
            image.save(save_path= output_path + '/' + str(page) + '.png')


        # 获取所有图片,拼接成pdf
        import img2pdf

        pdf_bytes = []
        for page in range(1, total_page + 1):
            with open(output_path + '/' + str(page) + '.png', 'rb') as f:
                img = f.read()
                pass
            pdf_bytes.append(img)
            pass

        pdf_bytes = img2pdf.convert(pdf_bytes)
        # pdf_path 添加权限
        # 获取pdf_path的dirname
        dirname = os.path.dirname(pdf_path)
        print(dirname)
        # 判断dirname是否存在
        if not os.path.exists(dirname):
            os.makedirs(dirname)
            pass
        # 设置dirname的权限
        os.chmod(dirname, 0o777)
        
        # 保存pdf
        with open(pdf_path, 'wb') as f:
            f.write(pdf_bytes)
            pass
        exit()
        # 发送邮件
        import requests
        
        res = requests.post(send_email_url, data={'flow_id': flow_id , 'pdf_path': pdf_path})
        
        print(str(flow_id) + "发送成功")
        # 删除redis中的数据
        redis_client.delete(key)
    time.sleep(10)