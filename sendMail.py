import os

import yagmail


def send_email(sender_email, sender_nickname, sender_password, receiver_email, subject, body):
    yag = yagmail.SMTP(user={sender_email: sender_nickname}, password=sender_password, host="smtp.qq.com")

    contents = body  # 邮件正文
    # files = [attachment_path]  # 发送ZIP文件

    yag.send(to=receiver_email, subject=subject, contents=contents)


# 发送邮箱
sender = '<EMAIL>'
password = 'eicvwvufqsrybfea'
nickname = '中国银行'
# 收件人
receiver = '<EMAIL>'
subject = '测试邮件主题'
body = '测试邮件内容'
              # /Users/<USER>/Project/jy/jy-admin/public/data/zg/121/KA020000072915014100001.zip
# attachment = '/Users/<USER>/Project/jy/jy-admin/public/data/zg/121/66.pdf'
# attachment = '/Users/<USER>/Project/jy/jy-admin/public/data/zg/121/66.pdf'
send_email(sender, nickname, password, receiver, subject, body)