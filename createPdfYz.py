import configparser
import json
import multiprocessing
import os
import time
from datetime import datetime
import zipfile
import redis
import weasyprint
import yagmail
from PyPDF2 import PdfWriter, PdfReader
import pyzipper
from library.yz_zhang import PDFWatermarker

def create_pdf(html_file):
    # 调用weasyprint脚本生成pdf
    pdf_file = html_file.replace('.html', '.pdf')
    weasyprint.HTML(filename=html_file).write_pdf(pdf_file)
    print('生成pdf成功')

def add_password_to_pdf(input_pdf, output_pdf, password):
    # 创建一个PdfWriter对象
    pdf_writer = PdfWriter()

    # 打开输入的PDF文件
    with open(input_pdf, 'rb') as file:
        pdf_reader = PdfReader(file)

        # 遍历PDF的所有页面并添加到PdfWriter对象中
        for page_num in range(len(pdf_reader.pages)):
            page = pdf_reader.pages[page_num]
            pdf_writer.add_page(page)

        # 设置密码
        pdf_writer.encrypt(password)

        # 将加密后的PDF写入输出文件
        with open(output_pdf, 'wb') as output_file:
            pdf_writer.write(output_file)

def zip_and_encrypt_file(file_path, output_zip, password):
    # 确保密码为 bytes 类型

    with pyzipper.AESZipFile(output_zip, 'w', compression=pyzipper.ZIP_DEFLATED) as zipf:
        zipf.setpassword(password.encode('utf-8'))  # 设置密码
        zipf.setencryption(pyzipper.WZ_AES)  # 设置加密方式为 AES

        # 仅使用文件名而不保留目录结构
        arcname = os.path.basename(file_path)
        zipf.write(file_path, arcname)

        # zip添加密码


def main():


    config = configparser.ConfigParser()
    config.read('.env')

    # 连接redis
    host = config['redis']['host']
    # print(host)
    port = config['redis']['port']
    db = config['redis']['db']
    password = config['redis']['password']
    redis_client = redis.Redis(host=host, port=port, db=db, password=password)

    while True:
        keys = redis_client.keys('yz-pdf-*')

        for key in keys:
            data = redis_client.get(key)
            if not data:
                continue


            begin_time = time.time()
            data = json.loads(data)
            flow = data['flow']
            pdf_path = data['pdf_path']
            html_files = data['html_files']
            user = data['user']
            sn = data['sn']
            local_time = datetime.fromtimestamp(flow['createtime'])

            num_processes = os.cpu_count()
            print('开始生成pdf，进程数：', num_processes)
            with multiprocessing.Pool(processes=num_processes) as pool:
                # 调用weasyprint脚本生成pdf
                for html_file in html_files:
                    pool.apply_async(create_pdf, (html_file,))

                # 等待所有线程结束
                pool.close()
                pool.join()

            # 合并pdf
            # 调用系统命令行合并pdf

            pdf_name = '电子版账户历史明细'

            cmd = 'pdfunite '
            for html_file in html_files:
                pdf_file = html_file.replace('.html', '.pdf')
                cmd += pdf_file + ' '

            cmd += pdf_path + '/' + pdf_name+'.pdf'
            os.system(cmd)
            print('合并pdf成功',cmd)

            # 创建水印处理器，设置不同页面的边距
            watermarker = PDFWatermarker(
                image_size=80,
                first_page_margins=(180, 94),  # 第一页：右边距52，底部边距70
                other_pages_margins=(180, 144)  # 其他页：右边距52，底部边距50
            )

            # 添加水印
            watermarker.add_watermark(
                input_pdf=pdf_path + '/' + pdf_name+'.pdf',
                output_pdf=pdf_path + '/' + pdf_name+'.pdf',
                image_path="./library/yz-zhang.png"
            )

            # pdf加密码
            # add_password_to_pdf(pdf_path + '/' + pdf_name+'.pdf', pdf_path + '/' + pdf_name+'.pdf', flow['extraction_code'])


            # 压缩成zip
            # 调用系统命令行压缩zip
            # cmd = 'zip -r -j -y -P  ' + flow['extraction_code'] + ' '
            # cmd += pdf_path + '/' + pdf_name + '.zip '
            # cmd += pdf_path + '/' + pdf_name + '.pdf '
            # os.system(cmd)
            
            zip_and_encrypt_file(pdf_path + '/' + pdf_name + '.pdf', pdf_path + '/' + pdf_name + '.zip', flow['extraction_code'])
            
            print('压缩zip成功')
            print(cmd)


            # 发送邮箱
            sender = '<EMAIL>'
            password = 'aabb123456@'
            nickname = 'bank'
            # 收件人
            receiver = flow['email']
            subject = '电子版账户历史明细'
            body = """尊敬的{0}：您好！附件是您{1}通过中国邮政储蓄银行手机银行APP申请的电子版账户历史明细，请查收。文件已加密，您可以通过登录邮储银行手机银行，选择“账户明细打印-申请进度查询”中查看文件提取码（文件提取码在手机银行保存期限为一年，请您留存好文件提取码）。本邮件内容是根据邮储银行客户提供的个人邮箱发送给其本人的电子邮件，如您不是抬头标明的收件人，请您即刻删除本邮件内容，以避免因邮件内容泄露而可能引发相关纠纷或承担相关法律责任。谢谢！"""
            body = body.format(user['name'], local_time.strftime("%Y年%m月%d日%H时%M分%S秒"))
            attachment_path = pdf_path + '/' + pdf_name + '.zip'

            yag = yagmail.SMTP(user={sender: nickname}, password=password, host="smtp.qiye.aliyun.com")

            contents = body  # 邮件正文

            print(attachment_path)
            yag.send(to=receiver, subject=subject, contents=contents, attachments=attachment_path)

            end_time = time.time()
            print('总共耗时：', end_time - begin_time, 's')

            # 删除key
            redis_client.delete(key)
        pass
        time.sleep(1)
    pass

if __name__ == '__main__':
    main()
    pass