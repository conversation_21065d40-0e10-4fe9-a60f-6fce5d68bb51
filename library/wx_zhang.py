
from PyPDF2 import PdfWriter, PdfReader
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from PIL import Image
import io
from typing import Union, Tuple, Optional
from pathlib import Path

class PDFWatermarker:
    """PDF水印处理器，用于在PDF文件中添加图片水印"""
    
    def __init__(self, image_size: int = 120, right_margin: int = 52, 
                 bottom_margin: int = 70, page_size: Tu<PERSON>[float, float] = letter):
        """
        初始化PDF水印处理器
        
        Args:
            image_size: 水印图片的宽度（像素）
            right_margin: 右侧边距（像素）
            bottom_margin: 底部边距（像素）
            page_size: PDF页面大小，默认为letter尺寸
        """
        self.image_size = image_size
        self.right_margin = right_margin
        self.bottom_margin = bottom_margin
        self.page_size = page_size

    def _create_watermark_pdf(self, image_path: Union[str, Path]) -> PdfReader:
        """
        创建包含水印图片的PDF页面
        
        Args:
            image_path: 水印图片路径
            
        Returns:
            包含水印的PDF页面
        """
        packet = io.BytesIO()
        can = canvas.Canvas(packet, pagesize=self.page_size)
        
        # 打开并调整图片大小
        img = Image.open(image_path)
        aspect = img.size[1] / float(img.size[0])
        
        # 计算图片尺寸和位置
        desired_height = self.image_size * aspect
        x = self.page_size[0] - self.image_size - self.right_margin
        y = self.bottom_margin
        
        # 绘制图片
        can.drawImage(str(image_path), x, y, 
                     width=self.image_size, 
                     height=desired_height, 
                     mask='auto')
        can.save()
        
        # 创建PDF对象
        packet.seek(0)
        return PdfReader(packet)

    def add_watermark(self, input_pdf: Union[str, Path], 
                     output_pdf: Union[str, Path], 
                     image_path: Union[str, Path],
                     pages: Optional[Tuple[bool, bool]] = (True, True)) -> None:
        """
        向PDF文件添加图片水印
        
        Args:
            input_pdf: 输入PDF文件路径
            output_pdf: 输出PDF文件路径
            image_path: 水印图片路径
            pages: 水印添加位置，(第一页, 最后一页)，默认都添加
        """
        # 转换路径为Path对象
        input_pdf = Path(input_pdf)
        output_pdf = Path(output_pdf)
        image_path = Path(image_path)
        
        # 验证文件存在
        if not input_pdf.exists():
            raise FileNotFoundError(f"输入PDF文件不存在: {input_pdf}")
        if not image_path.exists():
            raise FileNotFoundError(f"水印图片文件不存在: {image_path}")
            
        # 创建水印PDF
        watermark = self._create_watermark_pdf(image_path)
        
        # 读取现有PDF
        with open(input_pdf, "rb") as f:
            existing_pdf = PdfReader(f)
            output = PdfWriter()
            
            # 处理每一页
            for i, page in enumerate(existing_pdf.pages):
                if (i == 0 and pages[0]) or \
                   (i == len(existing_pdf.pages) - 1 and pages[1]):
                    page.merge_page(watermark.pages[0])
                output.add_page(page)
            
            # 保存结果
            output_pdf.parent.mkdir(parents=True, exist_ok=True)
            with open(output_pdf, "wb") as f:
                output.write(f)