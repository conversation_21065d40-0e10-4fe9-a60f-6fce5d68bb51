import os
import oss2
import time
import uuid
import configparser
from datetime import datetime
from urllib.parse import quote

class OssUploader:
    def __init__(self, config_path='.env'):
        """
        初始化OSS上传器，从.env文件读取配置
        
        Args:
            config_path: 配置文件路径，默认为.env
        """
        # 读取配置文件
        config = configparser.ConfigParser()
        config.read(config_path)
        
        # 从配置文件获取阿里云OSS配置
        self.access_key_id = config['aliyun']['access_key_id']
        self.access_key_secret = config['aliyun']['access_key_secret']
        self.endpoint = config['aliyun']['endpoint']
        self.bucket_name = config['aliyun']['bucket_name']
        # 添加自定义域名配置
        self.domain = config['aliyun'].get('domain', f'https://{self.bucket_name}.{self.endpoint}')
        
        # 初始化OSS客户端
        self.auth = oss2.Auth(self.access_key_id, self.access_key_secret)
        self.bucket = oss2.Bucket(self.auth, self.endpoint, self.bucket_name)
        
    def upload_file(self, local_file_path, original_filename=None, oss_file_path=None):
        """
        上传文件到OSS
        """
        try:
            # 使用时间戳+原始文件名作为OSS存储路径，放在files目录下
            file_name = original_filename or os.path.basename(local_file_path)
            timestamp = int(time.time())
            
            # 对文件名进行URL编码，确保OSS路径不包含特殊字符
            encoded_name = quote(file_name.encode('utf-8'))
            oss_file_path = f'files/{timestamp}_{encoded_name}'  # 添加files/前缀
            
            # 设置文件头
            headers = {
                'Content-Disposition': f'attachment; filename="{quote(file_name.encode("utf-8"))}"; filename*=UTF-8\'\'{quote(file_name.encode("utf-8"))}',
                'Content-Type': self._get_content_type(os.path.splitext(file_name)[1])
            }
            
            # 检查文件是否存在且可读
            if not os.path.exists(local_file_path):
                raise Exception(f"文件不存在: {local_file_path}")
            
            # 上传文件
            with open(local_file_path, 'rb') as file_obj:
                result = self.bucket.put_object(oss_file_path, file_obj, headers=headers)
                
                # 检查上传结果
                if result.status != 200:
                    raise Exception(f"文件上传失败，状态码: {result.status}")
            
            # 生成URL
            url = f'{self.domain}/{oss_file_path}'
            
            return {
                'code': 0,
                'msg': '上传成功',
                'data': {
                    'url': url,
                    'path': oss_file_path,
                    'filename': file_name,
                    'timestamp': timestamp
                }
            }
            
        except Exception as e:
            print(f"上传错误详情: {str(e)}")
            return {
                'code': 1,
                'msg': f'上传失败: {str(e)}',
                'data': None
            }
    
    def delete_file(self, oss_file_path):
        """
        删除OSS上的文件
        
        Args:
            oss_file_path: OSS上的文件路径
            
        Returns:
            dict: 操作结果
        """
        try:
            self.bucket.delete_object(oss_file_path)
            return {
                'code': 0,
                'msg': '删除成功',
                'data': None
            }
        except Exception as e:
            return {
                'code': 1,
                'msg': f'删除失败: {str(e)}',
                'data': None
            }
    
    def get_file_url(self, oss_file_path, filename=None):
        """
        获取文件的访问URL
        """
        try:
            url = f'{self.domain}/{oss_file_path}'
            
            return {
                'code': 0,
                'msg': '获取URL成功',
                'data': {
                    'url': url,
                    'filename': filename or os.path.basename(oss_file_path).split('_', 1)[1]
                }
            }
        except Exception as e:
            return {
                'code': 1,
                'msg': f'获取URL失败: {str(e)}',
                'data': None
            }

    def _get_content_type(self, file_ext):
        """
        根据文件扩展名获取Content-Type
        """
        content_types = {
            '.zip': 'application/zip',
            '.pdf': 'application/pdf',
            '.doc': 'application/msword',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.xls': 'application/vnd.ms-excel',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            # 可以根据需要添加更多类型
        }
        return content_types.get(file_ext.lower(), 'application/octet-stream')

    def create_symlink(self, target_file):
        """
        为文件创建软链接
        
        Args:
            target_file: 目标文件的OSS路径
        """
        try:
            # 生成UUID作为软链接名称
            symlink_key = f'{str(uuid.uuid4())}'
            
            # 从目标文件路径中提取原始文件名
            original_filename = os.path.basename(target_file).split('_', 1)[1]
            
            # 创建软链接，设置必需的headers
            headers = {
                'x-oss-symlink-target': target_file,
                'Content-Type': 'application/zip',  # 设置为zip类型
                'Content-Disposition': f'attachment; filename="{original_filename}"; filename*=UTF-8\'\'{original_filename}'
            }
            
            # 调用put_symlink创建软链接
            result = self.bucket.put_symlink(target_file, symlink_key, headers=headers)
            
            # 检查创建结果
            if result.status != 200:
                raise Exception(f"创建软链接失败，状态码: {result.status}")
            
            # 生成软链接URL
            symlink_url = f'{self.domain}/{symlink_key}'
            
            return {
                'code': 0,
                'msg': '创建软链接成功',
                'data': {
                    'url': symlink_url,
                    'uuid': symlink_key,
                    'target': target_file,
                    'filename': original_filename
                }
            }
        except Exception as e:
            return {
                'code': 1,
                'msg': f'创建软链接失败: {str(e)}',
                'data': None
            }


