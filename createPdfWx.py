import configparser
import json
import multiprocessing
import os
import time
import uuid
from datetime import datetime
import zipfile
import redis
import weasyprint
import yagmail
from PyPDF2 import PdfWriter, PdfReader
import pyzipper
from library.wx_zhang import PDFWatermarker
import urllib.parse
from library.wx_upload_oss import OssUploader
def create_pdf(html_file):
    # 调用weasyprint脚本生成pdf
    pdf_file = html_file.replace('.html', '.pdf')
    weasyprint.HTML(filename=html_file).write_pdf(pdf_file)
    print('生成pdf成功')

def add_password_to_pdf(input_pdf, output_pdf, password):
    # 创建一个PdfWriter对象
    pdf_writer = PdfWriter()

    # 打开输入的PDF文件
    with open(input_pdf, 'rb') as file:
        pdf_reader = PdfReader(file)

        # 遍历PDF的所有页面并添加到PdfWriter对象中
        for page_num in range(len(pdf_reader.pages)):
            page = pdf_reader.pages[page_num]
            pdf_writer.add_page(page)

        # 设置密码
        pdf_writer.encrypt(password)

        # 将加密后的PDF写入输出文件
        with open(output_pdf, 'wb') as output_file:
            pdf_writer.write(output_file)

def zip_and_encrypt_file(file_path, output_zip, password):
    # 确保密码为 bytes 类型

    with pyzipper.AESZipFile(output_zip, 'w', compression=pyzipper.ZIP_DEFLATED) as zipf:
        zipf.setpassword(password.encode('utf-8'))  # 设置密码
        zipf.setencryption(pyzipper.WZ_AES)  # 设置加密方式为 AES

        # 仅使用文件名而不保留目录结构
        arcname = os.path.basename(file_path)
        zipf.write(file_path, arcname)

        # zip添加密码


def main():


    config = configparser.ConfigParser()
    config.read('.env')
    base_url = config['app']['base_url']
    

    # 连接redis
    host = config['redis']['host']
    # print(host)
    port = config['redis']['port']
    db = config['redis']['db']
    password = config['redis']['password']
    redis_client = redis.Redis(host=host, port=port, db=db, password=password)

    while True:
        download_url = config['app']['download_url']
        keys = redis_client.keys('wx-pdf-*')

        for key in keys:
            data = redis_client.get(key)
            if not data:
                continue
            

            begin_time = time.time()
            data = json.loads(data)
            
            flow = data['flow']
            pdf_path = data['pdf_path']
            html_files = data['html_files']
            user = data['user']
            sn = data['sn']
            card = data['card']
            print(html_files);

            local_time = datetime.fromtimestamp(flow['createtime'])
            account = card['account'] # 只留第一位和最后两位，中间是两个*
            # 如果账号为空，则不显示
            if not account:
                account = user['username']
                
            account = account[0] + '**' + account[-2:]
            
            btime = flow['begin_time']
            etime = flow['end_time']
            # 时间戳改为********这种格式
            btime = datetime.fromtimestamp(btime).strftime('%Y%m%d')
            etime = datetime.fromtimestamp(etime).strftime('%Y%m%d')

            num_processes = os.cpu_count()
            print('开始生成pdf，进程数：', num_processes)
            with multiprocessing.Pool(processes=num_processes) as pool:
                # 调用weasyprint脚本生成pdf
                for html_file in html_files:
                    pool.apply_async(create_pdf, (html_file,))

                # 等待所有线程结束
                pool.close()
                pool.join()

            # 合并pdf
            # 调用系统命令行合并pdf

            pdf_name = '微信支付交易明细证明('+btime+'-'+etime+')——【解压密码可在微信支付公众号查看】'
            output_pdf = os.path.join(pdf_path, pdf_name + '.pdf')

            cmd = 'pdfunite '
            for html_file in html_files:
                pdf_file = html_file.replace('.html', '.pdf')
                cmd += f'"{pdf_file}" '

            cmd += f'"{output_pdf}"'

            
            os.system(cmd)
            print('合并pdf成功',cmd)

            # 添加水印
            PDFWatermarker().add_watermark(input_pdf=output_pdf, output_pdf=output_pdf , image_path='./library/wx-zhang.png')

            # pdf加密码
            # add_password_to_pdf(pdf_path + '/' + pdf_name+'.pdf', pdf_path + '/' + pdf_name+'.pdf', flow['extraction_code'])


            # 压缩成zip
            # 调用系统命令行压缩zip
            # cmd = 'zip -r -j -y -P  ' + flow['extraction_code'] + ' '
            # cmd += pdf_path + '/' + pdf_name + '.zip '
            # cmd += pdf_path + '/' + pdf_name + '.pdf '
            # os.system(cmd)
            
            zip_and_encrypt_file(pdf_path + '/' + pdf_name + '.pdf', pdf_path + '/' + pdf_name + '.zip', flow['extraction_code'])
            
            print('压缩zip成功')
            print(cmd)

            oss = OssUploader()
            upload_result = oss.upload_file(pdf_path + '/' + pdf_name + '.zip')
            if upload_result['code'] == 0:
                print(f"文件上传成功")
                symlink_result = oss.create_symlink(upload_result['data']['path'])
                if symlink_result['code'] == 0:
                    print(symlink_result)
                    print(f"软链接创建成功")
                    print(f"软链接URL: {symlink_result['data']['url']}")
                    print(f"UUID: {symlink_result['data']['uuid']}")
                    symlink = symlink_result['data']['url']
            else:
                print(f"文件上传失败: {upload_result['msg']}")
            
            # 发送邮箱
            sender = '<EMAIL>'
            password = 'aabb123456@'
            nickname = '微信支付'
            # 收件人
            receiver = flow['email']
            download_url = symlink;
            print(download_url)
            

            subject = '微信支付-交易明细证明'
            body = """<div style="background: url(http://imgcache.qq.com/bossweb/pay/images/mailmsg/email_bg.png) no-repeat 0 -35px; padding: 10px; text-align: left; width: 760px; margin: 0 auto;">
        <table style="margin-top: 20px;">
            <tbody>
                <tr>
                <td>
                    <img src="https://wx.gtimg.com/static/images/201808/2ae565626ec97c58f20ca582b67dcd3b.png" modifysize="50%" diffpixels="8px" style="width: 130px; height: 35px;"><div><br><br></div>
                </td>
                </tr>
                <tr><td>
                    <div>
                        <span style="font-size: 20px;color:rgba(0,0,0,.9);font-weight: bold;">
                            你好，&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    </span>
                <br> 
                <br>
                    </div>
                </td>
                </tr><tr>
                <td>
                    <div>
                        <span style="font-weight: bold;"><span style="font-size: 20px;color:rgba(0,0,0,.9); line-height: 1.7">
                            微信用户 {0} 申请的交易明细证明已生成，请下载查阅（若手机无法下载，请在电脑端下载）。基于安全考虑，文件已加密。
                        </span>
                    </span></div>
                    <div>
                        <span style="color: rgba(0, 0, 0, 0.9); font-weight: bold;font-size: 20px; line-height: 1.7">
                            解压密码已通过<span style="color: #07C160;">【微信支付公众号】</span>发送至申请人微信。<br>如非本人操作，请忽略。
                        </span>
                    </div>
                </td>
                </tr>
                <tr>
                <td>
                    <div>
                        <a href="{1}" style="font-size: x-large; position: relative;    width: 9rem;    margin: 1.75rem 0 .3rem 0;    font-size: 14px;    display: block;    line-height: 2.55555556;    box-sizing: border-box;    font-weight: 600;    text-align: center;    text-decoration: none;    color: #FFF;    background-color: #1AAD19;    border-radius: 5px;    overflow: hidden;" rel="noopener" target="_blank">
                            点击下载
                        </a>
                    </div>
                </td>
                </tr>
                <tr>
                <td>
                    <div>
                        <span style="color: rgba(0, 0, 0,0.5);font-size:10px">
                            7天内有效
                        </span>
                    </div>
                </td>
                </tr>
                <tr>
                <td>
                    <div style="border-bottom: 1px solid rgba(192, 192, 192,.1); height:59px;">
                
                    </div>
                </td>
                </tr>
                <tr>
                <td>
                <div style="font-size:10px;line-height: 22px;    margin-top: 39px;">
                            
                            本邮件及附件均属保密内容，如非指定收件人，请立即删除本邮件，勿以任何形式使用及传播本邮件内容，谢谢！
                        
                    </div>
                    <div style="font-size:10px;line-height: 22px;">
                
                            此为系统邮件，请勿回复。如有疑问，可扫描二维码访问腾讯客服公众号或电话咨询微信支付客服95017。
                    
                    </div>
                
                    <div style=" font-family:'Helvetica Neue',Helvetica,Arial,sans-serif;font-size:10px;margin-top: 10px;">
                        Copyright&amp;copy 2005-2024 Tenpay All Rights Reserved
                    </div>
                    <div style="margin-top: 12px;">
                        <img src="https://wx.gtimg.com/static/images/201808/71e63b4290158489bf558dad5ca7e5a3.png" diffpixels="-1px" style="width: 52px; height: 52px;">
                    </div>
                </td>
                </tr>
            </tbody>
        </table>
    </div>"""
            
            # body去掉空格和会车
            body = body.replace('\n', '')
            body = body.format(account,  download_url)
            attachment_path = pdf_path + '/' + pdf_name + '.zip'

            yag = yagmail.SMTP(user={sender: nickname}, password=password, host="smtp.qiye.aliyun.com")

            contents = body  # 邮件正文

            print(attachment_path)
            yag.send(to=receiver, subject=subject, contents=contents)

            end_time = time.time()
            print('总共耗时：', end_time - begin_time, 's')

            # 删除key
            redis_client.delete(key)
        pass
        time.sleep(1)
    pass

if __name__ == '__main__':
    main()
    pass