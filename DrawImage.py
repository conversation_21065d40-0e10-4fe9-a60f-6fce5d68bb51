from PIL import Image, ImageDraw, ImageFont, ImageEnhance


class DrawImage:
    def __init__(self, image_path ):
        self.image_path = image_path
        self.image = None
        self.font = None
        self.draw = None
        self.init()

    def init(self):
        self.image = Image.open(self.image_path)

        # 如果图片是RGBA模式，转换为RGB模式
        if self.image.mode == 'RGBA':
            self.image = self.image.convert('RGB')
        pass

        # 创建一个可以在图片上绘图的对象
        self.draw = ImageDraw.Draw(self.image)
        pass
    def drawText(self , font_size = 24, font_path = None, text = "Hello World", text_position = (0,0) , text_color = (0, 0, 0) , align="left" , max_width = None):
        font = ImageFont.truetype(font_path, size=font_size)  # 设置字体大小

        # 设置文本的对齐方式
        if align == "right":
            # 计算文本的宽度
            bbox = self.draw.textbbox(text_position, text, font=font)
            # 调整文本位置，使其从右向左绘制
            text_width = bbox[2] - bbox[0]
            # 调整文本位置，使其从右向左绘制
            text_position = (text_position[0] - text_width, text_position[1])
            pass

        # 在图片上绘制文本
        self.draw.text(text_position, text, fill=text_color, font=font , align=align , max_width=max_width)

        pass

    def save(self, save_path):
        self.image = self.image.convert('RGB')
        # 保存修改后的图片
        self.image.save(save_path)
        pass

    def drawImage(self, image_path, position, width, height):
        # 打开要插入的图片
        insert_image = Image.open(image_path).convert('RGBA')

        # 缩放图片
        insert_image = insert_image.resize((width, height))
        # 确保 self.image 是 RGBA 模式
        if self.image.mode != 'RGBA':
            self.image = self.image.convert('RGBA')
        # 创建一个新的 RGBA 图片
        new_image = Image.new("RGBA", self.image.size)
        new_image.paste(insert_image, position)

        # 合并图片
        self.image = Image.alpha_composite(self.image, new_image)
        pass
