import configparser
import datetime
import json
import os
import time

import redis
from PyPDF2 import PdfFileWriter, PdfFileReader, PdfWriter, Pdf<PERSON>eader


def add_password_to_pdf(input_pdf, output_pdf, password):
    # 创建一个PdfWriter对象
    pdf_writer = PdfWriter()

    # 打开输入的PDF文件
    with open(input_pdf, 'rb') as file:
        pdf_reader = PdfReader(file)

        # 遍历PDF的所有页面并添加到PdfWriter对象中
        for page_num in range(len(pdf_reader.pages)):
            page = pdf_reader.pages[page_num]
            pdf_writer.add_page(page)

        # 设置密码
        pdf_writer.encrypt(password)

        # 将加密后的PDF写入输出文件
        with open(output_pdf, 'wb') as output_file:
            pdf_writer.write(output_file)

while True:
    config = configparser.ConfigParser()
    config.read('.env')
    send_email_url = config['app']['ny_url']
    php_path = config['app']['php_path']
    # 连接redis
    host = config['redis']['host']
    # print(host)
    port = config['redis']['port']
    db = config['redis']['db']
    password = config['redis']['password']
    redis_client = redis.Redis(host=host, port=port, db=db , password=password)

    # 获取所有js-*的key
    keys = redis_client.keys('ny-pdf-*')
    for key in keys:
        # 获取value
        value = redis_client.get(key)
        value = json.loads(value)

        flow_id = value['flow_id']
        file_path = php_path + value['file_path']
        html_files = value['files']
        extraction_code = value['extraction_code']
        owner_code = value['owner_code']
        pdf_files = []

        for file in html_files:
            html_path = php_path + file
            pdf_path = html_path.replace('.html', '.pdf')

            # print('weasyprint ' + html_path +' ' + pdf_path)

            # os.system('weasyprint ' + html_path +' ' + pdf_path)
            # 加入html_files
            pdf_files.append(pdf_path)

        # KA0200000+9位数+00001
        #随机九位数
        random_code = str(int(time.time()))[-9:]
        # hqmx_20240727000256
        # 获取当前时间
        current_time = datetime.datetime.now()
        pdf_name = 'hqmx_' + str(current_time.strftime('%Y%m%d%H%M%S')) + '00001.pdf'
        pdf_file = os.path.join(file_path, pdf_name)

        # 合并pdf
        os.system('pdfunite ' + " ".join(pdf_files) + " " + pdf_file)
        print(pdf_file)
        # pdf加密码
        # add_password_to_pdf(pdf_file, pdf_file, extraction_code)

        # 发送邮件
        import requests
        print({'flow_id': flow_id , 'pdf_path': pdf_file})
        res = requests.post(send_email_url, data={'flow_id': flow_id , 'pdf_path': pdf_file})

        print(str(flow_id) + "发送成功")
        # 删除redis中的数据
        redis_client.delete(key)

    # 删除pdf文件
    time.sleep(3)

