import configparser
import json
import multiprocessing
import os
import time
from datetime import datetime

import redis
import weasyprint
import yagmail
from PyPDF2 import PdfWriter, Pdf<PERSON>eader

def create_pdf(html_file):
    # 调用weasyprint脚本生成pdf
    pdf_file = html_file.replace('.html', '.pdf')
    weasyprint.HTML(filename=html_file).write_pdf(pdf_file)
    print('生成pdf成功')

def add_password_to_pdf(input_pdf, output_pdf, password):
    # 创建一个PdfWriter对象
    pdf_writer = PdfWriter()

    # 打开输入的PDF文件
    with open(input_pdf, 'rb') as file:
        pdf_reader = PdfReader(file)

        # 遍历PDF的所有页面并添加到PdfWriter对象中
        for page_num in range(len(pdf_reader.pages)):
            page = pdf_reader.pages[page_num]
            pdf_writer.add_page(page)

        # 设置密码
        pdf_writer.encrypt(password)

        # 将加密后的PDF写入输出文件
        with open(output_pdf, 'wb') as output_file:
            pdf_writer.write(output_file)

def main():


    config = configparser.ConfigParser()
    config.read('.env')

    # 连接redis
    host = config['redis']['host']
    # print(host)
    port = config['redis']['port']
    db = config['redis']['db']
    password = config['redis']['password']
    redis_client = redis.Redis(host=host, port=port, db=db, password=password)

    while True:
        keys = redis_client.keys('ny-pdf-*')

        for key in keys:
            data = redis_client.get(key)
            if not data:
                continue
            begin_time = time.time()

            data = json.loads(data)
            flow = data['flow']
            pdf_path = data['pdf_path']
            html_files = data['html_files']
            user = data['user']
            sn = data['sn']
            local_time = datetime.fromtimestamp(flow['createtime'])

            num_processes = os.cpu_count()
            print('开始生成pdf，进程数：', num_processes)
            with multiprocessing.Pool(processes=num_processes) as pool:
                # 调用weasyprint脚本生成pdf
                for html_file in html_files:
                    pool.apply_async(create_pdf, (html_file,))

                # 等待所有线程结束
                pool.close()
                pool.join()

            # 合并pdf
            # 调用系统命令行合并pdf

            pdf_name = sn

            cmd = 'pdfunite '
            for html_file in html_files:
                pdf_file = html_file.replace('.html', '.pdf')
                cmd += pdf_file + ' '

            cmd += pdf_path + '/' + pdf_name+'.pdf'
            os.system(cmd)
            print('合并pdf成功',cmd)

            # pdf加密码
            # add_password_to_pdf(pdf_path + '/' + pdf_name+'.pdf', pdf_path + '/' + pdf_name+'.pdf', flow['extraction_code'])


            # 压缩成zip
            # 调用系统命令行压缩zip
            cmd = 'zip -r -j -P  ' + flow['extraction_code'] + ' '
            cmd += pdf_path + '/' + pdf_name + '.zip '
            cmd += pdf_path + '/' + pdf_name + '.pdf '
            os.system(cmd)
            print('压缩zip成功')
            print(cmd)

            # 发送邮箱
            sender = '<EMAIL>'
            password = 'aabb123456@'
            nickname = '中国农业银行掌上银行'
            # 收件人
            receiver = flow['email']
            subject = '中国农业银行-活期账户交易明细文件'
            body = """尊敬的{0}先生/女士:<br /> &nbsp;&nbsp;&nbsp; 您好！附件是您{1}通过农行掌上银行申请的电子版活期账户交易明细文件，请在电脑上下载查阅。基于安全考虑，文件已加密，文件解压码请在掌银“明细导出”功能的申请记录中查看。该明细内容仅供参考，不可作为凭证使用。<br /> 本邮件内容是客户隐私信息，如您并非抬头标明的收件人，请您即刻删除本邮件，勿以任何形式使用及传播本邮件内容，谢谢！ """
            body = body.format(user['name'], local_time.strftime("%Y年%m月%d日 %H:%M:%S"))
            attachment_path = pdf_path + '/' + pdf_name + '.zip'

            yag = yagmail.SMTP(user={sender: nickname}, password=password, host="smtp.qiye.aliyun.com")

            contents = body  # 邮件正文

            print(attachment_path)
            yag.send(to=receiver, subject=subject, contents=contents, attachments=attachment_path)

            end_time = time.time()
            print('总共耗时：', end_time - begin_time, 's')

            # 删除key
            redis_client.delete(key)
        pass
        time.sleep(1)
    pass

if __name__ == '__main__':
    main()
    pass